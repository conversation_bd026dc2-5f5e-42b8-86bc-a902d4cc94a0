#!/usr/bin/env tsx

/**
 * Benchmark script for chunked content loading performance
 */

import { performance } from 'perf_hooks'
import { NextMDXRemoteProvider } from '../src/services/content/providers/next-mdx-remote/provider'

interface BenchmarkResult {
  operation: string
  duration: number
  memoryUsed: number
  itemsLoaded: number
}

async function measureMemory(): Promise<number> {
  if (global.gc) {
    global.gc()
  }
  return process.memoryUsage().heapUsed / 1024 / 1024 // MB
}

async function benchmarkChunkedLoading(): Promise<BenchmarkResult[]> {
  const results: BenchmarkResult[] = []
  
  console.log('🚀 Starting chunked loading benchmark...\n')
  
  // Test 1: Provider initialization
  const initStart = performance.now()
  const memoryBefore = await measureMemory()
  
  const provider = new NextMDXRemoteProvider()
  
  const initEnd = performance.now()
  const memoryAfter = await measureMemory()
  
  results.push({
    operation: 'Provider Initialization',
    duration: initEnd - initStart,
    memoryUsed: memoryAfter - memoryBefore,
    itemsLoaded: 0
  })
  
  // Test 2: First content access (triggers chunk loading)
  const firstAccessStart = performance.now()
  const memoryBeforeFirst = await measureMemory()
  
  const firstContent = await provider.getContent('blog', 'getting-started-with-shipany', 'en')
  
  const firstAccessEnd = performance.now()
  const memoryAfterFirst = await measureMemory()
  
  results.push({
    operation: 'First Content Access (Chunk Loading)',
    duration: firstAccessEnd - firstAccessStart,
    memoryUsed: memoryAfterFirst - memoryBeforeFirst,
    itemsLoaded: firstContent ? 1 : 0
  })
  
  // Test 3: Subsequent content access (cached)
  const cachedAccessStart = performance.now()
  const memoryBeforeCached = await measureMemory()
  
  const cachedContent = await provider.getContent('blog', 'english-only-test', 'en')
  
  const cachedAccessEnd = performance.now()
  const memoryAfterCached = await measureMemory()
  
  results.push({
    operation: 'Cached Content Access',
    duration: cachedAccessEnd - cachedAccessStart,
    memoryUsed: memoryAfterCached - memoryBeforeCached,
    itemsLoaded: cachedContent ? 1 : 0
  })
  
  // Test 4: Content list (multiple items)
  const listStart = performance.now()
  const memoryBeforeList = await measureMemory()
  
  const contentList = await provider.getContentList('blog', 'en')
  
  const listEnd = performance.now()
  const memoryAfterList = await measureMemory()
  
  results.push({
    operation: 'Content List (Multiple Items)',
    duration: listEnd - listStart,
    memoryUsed: memoryAfterList - memoryBeforeList,
    itemsLoaded: contentList.length
  })
  
  // Test 5: Different content type (new chunk)
  const newChunkStart = performance.now()
  const memoryBeforeNewChunk = await measureMemory()
  
  const productContent = await provider.getContent('product', 'ai-content-generator', 'en')
  
  const newChunkEnd = performance.now()
  const memoryAfterNewChunk = await measureMemory()
  
  results.push({
    operation: 'New Content Type (New Chunk)',
    duration: newChunkEnd - newChunkStart,
    memoryUsed: memoryAfterNewChunk - memoryBeforeNewChunk,
    itemsLoaded: productContent ? 1 : 0
  })
  
  return results
}

function formatResults(results: BenchmarkResult[]): void {
  console.log('📊 Benchmark Results:\n')
  console.log('┌─────────────────────────────────────┬──────────────┬──────────────┬──────────────┐')
  console.log('│ Operation                           │ Duration (ms)│ Memory (MB)  │ Items Loaded │')
  console.log('├─────────────────────────────────────┼──────────────┼──────────────┼──────────────┤')
  
  results.forEach(result => {
    const operation = result.operation.padEnd(35)
    const duration = result.duration.toFixed(2).padStart(12)
    const memory = result.memoryUsed.toFixed(2).padStart(12)
    const items = result.itemsLoaded.toString().padStart(12)
    
    console.log(`│ ${operation} │ ${duration} │ ${memory} │ ${items} │`)
  })
  
  console.log('└─────────────────────────────────────┴──────────────┴──────────────┴──────────────┘')
  
  // Summary
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0)
  const totalMemory = results.reduce((sum, r) => sum + Math.max(0, r.memoryUsed), 0)
  const totalItems = results.reduce((sum, r) => sum + r.itemsLoaded, 0)
  
  console.log('\n📈 Summary:')
  console.log(`   Total Duration: ${totalDuration.toFixed(2)}ms`)
  console.log(`   Total Memory: ${totalMemory.toFixed(2)}MB`)
  console.log(`   Total Items: ${totalItems}`)
  console.log(`   Avg per Item: ${(totalDuration / totalItems).toFixed(2)}ms`)
}

async function main() {
  try {
    const results = await benchmarkChunkedLoading()
    formatResults(results)
    
    console.log('\n✅ Benchmark completed successfully!')
    console.log('\n💡 Key Insights:')
    console.log('   • First access loads chunks (higher duration/memory)')
    console.log('   • Cached access is much faster')
    console.log('   • New content types trigger new chunk loading')
    console.log('   • Memory usage is optimized through chunking')
    
  } catch (error) {
    console.error('❌ Benchmark failed:', error)
    process.exit(1)
  }
}

// Run the benchmark
main()
